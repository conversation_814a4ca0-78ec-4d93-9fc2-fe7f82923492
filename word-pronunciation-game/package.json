{"name": "word-pronunciation-game", "version": "1.0.0", "private": true, "description": "English word pronunciation practice mini-program with voice recognition", "templateInfo": {"name": "default", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"prepare": "husky", "new": "taro new", "build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch"}, "browserslist": {"development": ["defaults and fully supports es6-module", "maintained node versions"], "production": ["last 3 versions", "Android >= 4.1", "ios >= 8"]}, "author": "", "dependencies": {"@babel/runtime": "^7.24.4", "@tarojs/components": "4.1.4", "@tarojs/helper": "4.1.4", "@tarojs/plugin-platform-weapp": "4.1.4", "@tarojs/plugin-platform-alipay": "4.1.4", "@tarojs/plugin-platform-tt": "4.1.4", "@tarojs/plugin-platform-swan": "4.1.4", "@tarojs/plugin-platform-jd": "4.1.4", "@tarojs/plugin-platform-qq": "4.1.4", "@tarojs/plugin-platform-h5": "4.1.4", "@tarojs/plugin-platform-harmony-hybrid": "4.1.4", "@tarojs/runtime": "4.1.4", "@tarojs/shared": "4.1.4", "@tarojs/taro": "4.1.4", "@tarojs/plugin-framework-react": "4.1.4", "@tarojs/react": "4.1.4", "react-dom": "^18.0.0", "react": "^18.0.0"}, "devDependencies": {"@tarojs/plugin-generator": "4.1.4", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "lint-staged": "^16.1.2", "husky": "^9.1.7", "stylelint-config-standard": "^38.0.0", "@babel/core": "^7.24.4", "@tarojs/cli": "4.1.4", "@babel/plugin-transform-class-properties": "7.25.9", "@types/webpack-env": "^1.13.6", "@types/react": "^18.0.0", "webpack": "5.91.0", "@tarojs/taro-loader": "4.1.4", "@tarojs/webpack5-runner": "4.1.4", "sass": "^1.75.0", "babel-preset-taro": "4.1.4", "eslint-config-taro": "4.1.4", "eslint": "^8.57.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "react-refresh": "^0.14.0", "@babel/preset-react": "^7.24.1", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.4.0", "stylelint": "^16.4.0", "typescript": "^5.4.5", "tsconfig-paths-webpack-plugin": "^4.1.0", "postcss": "^8.4.38", "@types/node": "^18"}}